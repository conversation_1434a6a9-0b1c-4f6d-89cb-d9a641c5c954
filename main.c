/*
 * main.c - 太阳能逆变器主程序
 *
 * 开发环境: CCS Studio 11.0.0
 * 编译器版本: TI v22.6.0.LTS
 * 目标芯片: TMS320F28335 DSP
 *
 * 功能描述:
 * 1. 系统初始化 - GPIO、定时器、ADC、PWM等外设初始化
 * 2. 中断服务程序 - 定时器中断和串口中断处理
 * 3. 主循环 - PWM死区时间设置和太阳能控制循环
 * 4. 调试功能 - 执行时间监控和性能分析
 */

#include "common.h"
#include "solar.h"

// ========== 函数声明 ==========
void Gpio_setup();
__interrupt void cpu_timer0_isr(void);
__interrupt void cpu_timer2_isr(void);
__interrupt void SCIA_RXINT(void);
__interrupt void SCIB_RXINT(void);

// ========== 全局变量定义 ==========
int dead_time = 225; // PWM死区时间设置：保护参数防止上下开关管同时导通造成短路
                     // 实际死区时间 = 此值/2 = 112.5个TBCLK周期，约1.5微秒@75MHz
                     // 计算公式：112.5 × (1/75MHz) = 1.5μs (TBCLK经HSPCLKDIV=2分频后为75MHz)

// ========== ADC时钟配置宏定义 ==========
#if (CPU_FRQ_150MHZ) // 默认系统时钟 - 150 MHz SYSCLKOUT
// HSPCLK = SYSCLKOUT/2*ADC_MODCLK2 = 150/(2*3) = 25.0 MHz
#define ADC_MODCLK 0x3 // ADC模块时钟分频系数
#endif

/**
 * @brief 主函数 - 太阳能逆变器程序入口
 * @return int 程序返回值 (实际上不会返回)
 *
 * 主要功能:
 * 1. 系统初始化 - 时钟、GPIO、ADC、PWM等
 * 2. 中断配置 - 定时器中断和串口中断
 * 3. 主循环 - PWM死区控制和太阳能算法处理
 */
int main(void)
{
    // ========== 第一步：基础系统初始化 ==========
    solar_Init();   // 初始化太阳能控制参数和变量
    InitSysCtrl();  // 初始化系统控制模块(时钟配置等)

    // ========== 第二步：ADC时钟配置 ==========
    EALLOW;  // 启用EALLOW保护寄存器访问
    SysCtrlRegs.HISPCP.all = ADC_MODCLK; // 设置ADC高速时钟: HSPCLK = SYSCLKOUT/ADC_MODCLK
    EDIS;    // 禁用EALLOW保护寄存器访问

    // ========== 第三步：GPIO基础初始化 ==========
    InitGpio(); // 初始化GPIO模块

    // ========== 第四步：专用GPIO引脚配置 ==========
    // 输出过流保护信号 (OUTOCP) - GPIO12
    GpioCtrlRegs.GPAPUD.bit.GPIO12 = 0;   // 禁用上拉电阻
    GpioDataRegs.GPACLEAR.bit.GPIO12 = 1; // 输出低电平
    GpioCtrlRegs.GPAMUX1.bit.GPIO12 = 0;  // 配置为普通GPIO功能
    GpioCtrlRegs.GPADIR.bit.GPIO12 = 1;   // 配置为输出方向

    // 锁定信号 (LOCK) - GPIO17
    GpioCtrlRegs.GPAPUD.bit.GPIO17 = 0;   // 禁用上拉电阻
    GpioDataRegs.GPASET.bit.GPIO17 = 1;   // 输出高电平
    GpioCtrlRegs.GPAMUX2.bit.GPIO17 = 0;  // 配置为普通GPIO功能
    GpioCtrlRegs.GPADIR.bit.GPIO17 = 1;   // 配置为输出方向

    // RS485通信方向控制 - GPIO49
    GpioCtrlRegs.GPBPUD.bit.GPIO49 = 0;    // 禁用上拉电阻
    GpioDataRegs.GPBCLEAR.bit.GPIO49 = 1;  // 输出低电平(接收模式)
    GpioCtrlRegs.GPBMUX2.bit.GPIO49 = 0;   // 配置为普通GPIO功能
    GpioCtrlRegs.GPBDIR.bit.GPIO49 = 1;    // 配置为输出方向

    Gpio_setup(); // 配置LED和其他专用GPIO引脚

    // ========== 第五步：中断系统初始化 ==========
    DINT; // 禁用全局中断

    InitPieCtrl(); // 初始化PIE(外设中断扩展)控制模块
    IER = 0x0000;  // 禁用所有PIE中断
    IFR = 0x0000;  // 清除所有PIE中断标志

    // ========== 第六步：系统功能模块初始化 ==========
    InitPieVectTable(); // 初始化PIE中断向量表
    MemCopy(&RamfuncsLoadStart, &RamfuncsLoadEnd, &RamfuncsRunStart); // 复制函数到RAM
    InitFlash(); // 初始化Flash存储器

    // ========== 第七步：外设模块初始化 ==========
    // InitPeripherals(); // 初始化所有外设(本例中不需要)
    InitAdc();   // 初始化ADC模数转换器
    InitEPwm();  // 初始化增强型PWM模块
    InitECap();  // 初始化增强型捕获模块

    ECap2Regs.CAP4 = 0; // 清零捕获寄存器4

    // ========== 第八步：中断向量表配置 ==========
    EALLOW; // 启用EALLOW保护寄存器访问
    PieVectTable.TINT0 = &cpu_timer0_isr;      // 配置定时器0中断向量
    PieVectTable.TINT2 = &cpu_timer2_isr;      // 配置定时器2中断向量
    PieVectTable.SCIRXINTA = &SCIA_RXINT;      // 配置串口A接收中断向量
    PieVectTable.SCIRXINTB = &SCIB_RXINT;      // 配置串口B接收中断向量
    EDIS; // 禁用EALLOW保护寄存器访问

    // ========== 第九步：CPU定时器配置 ==========
    InitCpuTimers(); // 初始化CPU定时器模块

    // 配置定时器0: 1ms周期中断 (用于控制算法)
    ConfigCpuTimer(&CpuTimer0, 150, 20000.0f / TIM0_SIN_PRD_CNT); // 150MHz时钟, 1ms周期

    // 配置定时器1: 用于执行时间测量
    ConfigCpuTimer(&CpuTimer1, 150, 10000000); // 150MHz时钟, 长周期计时器

    // 配置定时器2: 50us周期中断 (用于高频控制)
    ConfigCpuTimer(&CpuTimer2, 150, 20000.0f / TIM2_SIN_PRD_CNT); // 150MHz时钟, 50us周期

    // ========== 第十步：定时器启动配置 ==========
    CpuTimer0Regs.TCR.all = 0x4000; // 配置定时器0控制寄存器
    CpuTimer1Regs.TCR.all = 0x4000; // 配置定时器1控制寄存器
    CpuTimer2Regs.TCR.all = 0x4000; // 配置定时器2控制寄存器
    CpuTimer2Regs.TCR.bit.TSS = 0;  // 启动定时器2

    // ========== 第十一步：中断使能配置 ==========
    IER |= M_INT1;  // 使能CPU中断1 (连接到定时器0)
    IER |= M_INT14; // 使能CPU中断14 (连接到定时器2)

    PieCtrlRegs.PIEIER1.bit.INTx7 = 1; // 使能PIE组1中断7 (定时器0中断)

    // ========== 第十二步：全局中断使能 ==========
    EINT; // 使能全局中断 INTM
    ERTM; // 使能全局实时中断 DBGM

    // ========== 第十三步：PWM输出使能 ==========
    PWMOutputsEnable(); // 使能PWM输出
    // PWMOutputsDisable(); // 禁用PWM输出(备用)

    ECap2Regs.CAP4 = 0; // 清零捕获寄存器

    // ========== 第十四步：PWM死区控制配置 ==========
    // 配置PWM1死区控制
    EPwm1Regs.DBCTL.bit.IN_MODE = 0;  // 死区输入模式: EPWMxA作为输入源
    EPwm1Regs.DBCTL.bit.POLSEL = 2;   // 极性选择: AH和AL都不反相
    EPwm1Regs.DBCTL.bit.OUT_MODE = 3; // 输出模式: 死区全使能

    // 配置PWM2死区控制
    EPwm2Regs.DBCTL.bit.IN_MODE = 0;  // 死区输入模式: EPWMxA作为输入源
    EPwm2Regs.DBCTL.bit.POLSEL = 2;   // 极性选择: AH和AL都不反相
    EPwm2Regs.DBCTL.bit.OUT_MODE = 3; // 输出模式: 死区全使能

    // 配置PWM3死区控制
    EPwm3Regs.DBCTL.bit.IN_MODE = 0;  // 死区输入模式: EPWMxA作为输入源
    EPwm3Regs.DBCTL.bit.POLSEL = 2;   // 极性选择: AH和AL都不反相
    EPwm3Regs.DBCTL.bit.OUT_MODE = 3; // 输出模式: 死区全使能

    // 配置PWM4死区控制
    EPwm4Regs.DBCTL.bit.IN_MODE = 0;  // 死区输入模式: EPWMxA作为输入源
    EPwm4Regs.DBCTL.bit.POLSEL = 2;   // 极性选择: AH和AL都不反相
    EPwm4Regs.DBCTL.bit.OUT_MODE = 3; // 输出模式: 死区全使能

    // 配置PWM5死区控制
    EPwm5Regs.DBCTL.bit.IN_MODE = 0;  // 死区输入模式: EPWMxA作为输入源
    EPwm5Regs.DBCTL.bit.POLSEL = 2;   // 极性选择: AH和AL都不反相
    EPwm5Regs.DBCTL.bit.OUT_MODE = 3; // 输出模式: 死区全使能

    // 配置PWM6死区控制
    EPwm6Regs.DBCTL.bit.IN_MODE = 0;  // 死区输入模式: EPWMxA作为输入源
    EPwm6Regs.DBCTL.bit.POLSEL = 2;   // 极性选择: AH和AL都不反相
    EPwm6Regs.DBCTL.bit.OUT_MODE = 3; // 输出模式: 死区全使能

    // ========== 第十五步：串口通信初始化 ==========
    Solar_Scia_Init(115200); // 初始化串口A, 波特率115200 (用于调试通信)
    Solar_Scib_Init(9600);   // 初始化串口B, 波特率9600 (用于设备通信)

    // ========== 主循环 - 系统运行核心 ==========
    while (1)
    {
        // ========== PWM死区时间动态设置 ==========
        // 为所有6个PWM通道设置死区时间，防止上下桥臂同时导通
        // 实际死区时间 = (dead_time/2) × (1/75MHz) = 112.5 × 13.33ns = 1.5μs
        EPwm1Regs.DBFED = dead_time / 2; // PWM1下降沿死区时间 (112.5个TBCLK周期)
        EPwm1Regs.DBRED = dead_time / 2; // PWM1上升沿死区时间 (112.5个TBCLK周期)
        EPwm2Regs.DBFED = dead_time / 2; // PWM2下降沿死区时间 (112.5个TBCLK周期)
        EPwm2Regs.DBRED = dead_time / 2; // PWM2上升沿死区时间 (112.5个TBCLK周期)
        EPwm3Regs.DBFED = dead_time / 2; // PWM3下降沿死区时间 (112.5个TBCLK周期)
        EPwm3Regs.DBRED = dead_time / 2; // PWM3上升沿死区时间 (112.5个TBCLK周期)
        EPwm4Regs.DBFED = dead_time / 2; // PWM4下降沿死区时间 (112.5个TBCLK周期)
        EPwm4Regs.DBRED = dead_time / 2; // PWM4上升沿死区时间 (112.5个TBCLK周期)
        EPwm5Regs.DBFED = dead_time / 2; // PWM5下降沿死区时间 (112.5个TBCLK周期)
        EPwm5Regs.DBRED = dead_time / 2; // PWM5上升沿死区时间 (112.5个TBCLK周期)
        EPwm6Regs.DBFED = dead_time / 2; // PWM6下降沿死区时间 (112.5个TBCLK周期)
        EPwm6Regs.DBRED = dead_time / 2; // PWM6上升沿死区时间 (112.5个TBCLK周期)

        // ========== 太阳能控制主循环 ==========
        solar_Loop(); // 执行太阳能逆变器控制算法

        // ========== 性能监控和调试 ==========
        // 计算定时器2中断执行时间 (单位: 微秒)
        hdebug.time2_count = (float)hdebug.TIM2_time_count / 150;
        if (hdebug.time2_count > hdebug.time2_count_max && hdebug.time2_count < 1000000)
        {
            hdebug.time2_count_max = hdebug.time2_count; // 记录最大执行时间
        }

        // 计算定时器0中断执行时间 (单位: 微秒)
        hdebug.time0_count = (float)hdebug.TIM0_time_count / 150;
        if (hdebug.time0_count > hdebug.time0_count_max && hdebug.time0_count < 1000000)
        {     
            hdebug.time0_count_max = hdebug.time0_count; // 记录最大执行时间
        }
    }
}

/**
 * @brief 串口A接收中断服务程序
 *
 * 功能: 处理串口A接收到的数据
 * 触发条件: 串口A接收缓冲区有数据时触发
 * 用途: 主要用于调试通信和参数设置
 */
__interrupt void SCIA_RXINT(void)
{
    EINT; // 允许中断嵌套，提高系统响应性

    // 处理接收到的数据
    SCI_SR_RxByte(&hScia, SciaRegs.SCIRXBUF.bit.RXDT); // 读取接收数据并处理

    // 清除中断标志
    SciaRegs.SCIFFRX.bit.RXFFOVRCLR = 1;  // 清除接收FIFO溢出标志
    SciaRegs.SCIFFRX.bit.RXFFINTCLR = 1;  // 清除接收FIFO中断标志
    PieCtrlRegs.PIEACK.all |= PIEACK_GROUP9; // 应答PIE组9中断
}

/**
 * @brief 串口B接收中断服务程序
 *
 * 功能: 处理串口B接收到的数据
 * 触发条件: 串口B接收缓冲区有数据时触发
 * 用途: 主要用于与外部设备通信
 */
__interrupt void SCIB_RXINT(void)
{
    EINT; // 允许中断嵌套，提高系统响应性

    // 处理接收到的数据
    SCI_SR_RxByte(&hScib, ScibRegs.SCIRXBUF.bit.RXDT); // 读取接收数据并处理

    // 清除中断标志
    ScibRegs.SCIFFRX.bit.RXFFOVRCLR = 1;  // 清除接收FIFO溢出标志
    ScibRegs.SCIFFRX.bit.RXFFINTCLR = 1;  // 清除接收FIFO中断标志
    PieCtrlRegs.PIEACK.all |= PIEACK_GROUP9; // 应答PIE组9中断
}

/**
 * @brief CPU定时器0中断服务程序
 *
 * 功能: 1ms周期的控制算法中断
 * 触发频率: 1000Hz (每1ms触发一次)
 * 主要用途: 执行太阳能逆变器的主要控制算法
 */
__interrupt void cpu_timer0_isr(void)
{
    EINT; // 允许中断嵌套，确保高优先级中断能够响应

    CpuTimer0.InterruptCount++; // 中断计数器递增

    // 执行控制算法并测量执行时间
    {
        uint32_t temp = CpuTimer1.RegsAddr->TIM.all; // 记录开始时间
        solar_ISR_1ms(); // 执行1ms周期的太阳能控制算法
        hdebug.TIM0_time_count = (temp - CpuTimer1.RegsAddr->TIM.all); // 计算执行时间
    }

    PieCtrlRegs.PIEACK.all = PIEACK_GROUP1; // 应答PIE组1中断
}

/**
 * @brief CPU定时器2中断服务程序
 *
 * 功能: 50us周期的高频控制中断
 * 触发频率: 20kHz (每50us触发一次)
 * 主要用途: 执行高频PWM控制和快速保护算法
 */
__interrupt void cpu_timer2_isr(void)
{
    CpuTimer2.InterruptCount++; // 中断计数器递增

    // 执行高频控制算法并测量执行时间
    {
        uint32_t temp = CpuTimer1.RegsAddr->TIM.all; // 记录开始时间
        solar_ISR_50us(); // 执行50us周期的高频控制算法
        hdebug.TIM2_time_count = (temp - CpuTimer1.RegsAddr->TIM.all); // 计算执行时间
    }
}

/**
 * @brief GPIO引脚专用配置函数
 *
 * 功能: 配置LED指示灯、继电器控制和调试引脚
 * 调用时机: 在主函数初始化阶段调用
 */
void Gpio_setup(void)
{
    EALLOW; // 启用EALLOW保护寄存器访问

    // ========== LED指示灯配置 - GPIO73 ==========
    GpioCtrlRegs.GPCPUD.bit.GPIO73 = 0;   // 禁用上拉电阻
    GpioDataRegs.GPCSET.bit.GPIO73 = 1;   // 输出高电平(LED熄灭)
    GpioCtrlRegs.GPCMUX1.bit.GPIO73 = 0;  // 配置为普通GPIO功能
    GpioCtrlRegs.GPCDIR.bit.GPIO73 = 1;   // 配置为输出方向

    // ========== 并网继电器控制 - GPIO72 ==========
    // 用于控制ABC三相并网继电器的开关
    GpioCtrlRegs.GPCPUD.bit.GPIO72 = 0;    // 禁用上拉电阻
    GpioDataRegs.GPCCLEAR.bit.GPIO72 = 1;  // 输出低电平(继电器断开)
    GpioCtrlRegs.GPCMUX1.bit.GPIO72 = 0;   // 配置为普通GPIO功能
    GpioCtrlRegs.GPCDIR.bit.GPIO72 = 1;    // 配置为输出方向

    // ========== 调试监控引脚 - GPIO75 ==========
    // 用于示波器监控中断函数执行时间
    GpioCtrlRegs.GPCPUD.bit.GPIO75 = 0;    // 禁用上拉电阻
    GpioDataRegs.GPCCLEAR.bit.GPIO75 = 1;  // 输出低电平
    GpioCtrlRegs.GPCMUX1.bit.GPIO75 = 0;   // 配置为普通GPIO功能
    GpioCtrlRegs.GPCDIR.bit.GPIO75 = 1;    // 配置为输出方向

    EDIS; // 禁用EALLOW保护寄存器访问
}

//
// End of File
//
