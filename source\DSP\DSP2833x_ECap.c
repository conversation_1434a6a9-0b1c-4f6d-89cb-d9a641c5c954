// TI File $Revision: /main/2 $
// Checkin $Date: March 15, 2007   16:54:36 $
//###########################################################################
//
// FILE:   DSP2833x_ECap.c
//
// TITLE:  DSP2833x eCAP Initialization & Support Functions.
//
//###########################################################################
// $TI Release: 2833x/2823x Header Files V1.32 $
// $Release Date: June 28, 2010 $
// $Copyright:
// Copyright (C) 2009-2021 Texas Instruments Incorporated - http://www.ti.com/
//
// Redistribution and use in source and binary forms, with or without 
// modification, are permitted provided that the following conditions 
// are met:
// 
//   Redistributions of source code must retain the above copyright 
//   notice, this list of conditions and the following disclaimer.
// 
//   Redistributions in binary form must reproduce the above copyright
//   notice, this list of conditions and the following disclaimer in the 
//   documentation and/or other materials provided with the   
//   distribution.
// 
//   Neither the name of Texas Instruments Incorporated nor the names of
//   its contributors may be used to endorse or promote products derived
//   from this software without specific prior written permission.
// 
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS 
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT 
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT 
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, 
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT 
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT 
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE 
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
// $
//###########################################################################

//
// Included Files
//
#include "DSP2833x_Device.h"     // DSP2833x Headerfile Include File
#include "DSP2833x_Examples.h"   // DSP2833x Examples Include File
#include "SC_define.h"//new
//
// InitECap - This function initializes the eCAP(s) to a known state.
//
/*void
InitECap(void)
{
    //
    // Initialize eCAP1/2/3
    //
}*/
void InitECap(void)
{
    // Setup APWM mode on eCAP1, set period and compare registers
    ECap1Regs.ECCTL2.bit.CAP_APWM  = 1;             // Active low//工作在APWM模式
    // 0 ECAP module operates in capture mode. This mode forces the following configuration:
        //· Inhibits TSCTR resets via CTR = PRD event
        //· Inhibits shadow loads on CAP1 and 2 registers
        //· Permits user to enable CAP1-4 register load
        //· CAPx/APWMx pin operates as a capture input
    // 1 ECAP module operates in APWM mode. This mode forces the following configuration:
        //· Resets TSCTR on CTR = PRD event (period boundary
        //· Permits shadow loading on CAP1 and 2 registers
        //· Disables loading of time-stamps into CAP1-4 registers
        //· CAPx/APWMx pin operates as a APWM output

    ECap1Regs.ECCTL2.bit.APWMPOL   = 0;//高电平有效(比较值决定高电平的时间，先高后低)
    // 0, Output is active high;
    // 1, Output is active low;

    ECap1Regs.ECCTL2.bit.SYNCI_EN  = 0;//禁止同步功能     //1;             // Enable sync mode
    // 0, Disable sync-in option
    // 1, Enable counter(TSCTR) to be loaded from CTRPHS register upon either a SYNCI signal or a S/W force event

    ECap1Regs.ECCTL2.bit.SYNCO_SEL = 2;//禁止同步输出     //0;
    // 00,select sync-in event to be the sync-out signal (pass through)
    // 01,select CTR = PRD event to be the sync-out signal
    // 10,Disable sync out signal
    // 11,Disable sync out signal

    ECap1Regs.ECEINT.all           = 0;             // enable Compare Equal Int//时钟清零
    // bit15-8      0's:    reserved
    // bit7         0:      CTR=CMP, 0 = compare interrupt disabled
    // bit6         0:      CTR=PRD, 0 = period interrupt disabled
    // bit5         0:      CTROVF,  0 = overflow interrupt disabled
    // bit4         0:      CEVT4,   0 = event 4 interrupt disabled
    // bit3         0:      CEVT3,   0 = event 3 interrupt disabled
    // bit2         0:      CEVT2,   0 = event 2 interrupt disabled
    // bit1         0:      CEVT1,   0 = event 1 interrupt disabled
    // bit0         0:      reserved

    ECap1Regs.CAP1 = APWM_PERIOD-1;         // Set Period value, 3750 = 150MHz/40kHz//PWM周期为10K个时钟周期
    ECap1Regs.CAP2 = 0;//500;           // Set Compare value
    ECap1Regs.CTRPHS = 0;//1875;        // delay 5.7us


    ECap2Regs.ECCTL2.bit.CAP_APWM = 1;  // Active low
    // 0 ECAP module operates in capture mode. This mode forces the following configuration:
        //· Inhibits TSCTR resets via CTR = PRD event
        //· Inhibits shadow loads on CAP1 and 2 registers
        //· Permits user to enable CAP1-4 register load
        //· CAPx/APWMx pin operates as a capture input
    // 1 ECAP module operates in APWM mode. This mode forces the following configuration:
        //· Resets TSCTR on CTR = PRD event (period boundary
        //· Permits shadow loading on CAP1 and 2 registers
        //· Disables loading of time-stamps into CAP1-4 registers
        //· CAPx/APWMx pin operates as a APWM output

    ECap2Regs.ECCTL2.bit.APWMPOL = 0;
    // 0, Output is active high;
    // 1, Output is active low;
    ECap2Regs.ECCTL2.bit.SYNCI_EN = 0;//1;  // Enable sync mode
    // 0, Disable sync-in option
    // 1, Enable counter(TSCTR) to be loaded from CTRPHS register upon either a SYNCI signal or a S/W force event
    ECap2Regs.ECCTL2.bit.SYNCO_SEL = 2;//0;
    // 00,select sync-in event to be the sync-out signal (pass through)
    // 01,select CTR = PRD event to be the sync-out signal
    // 10,Disable sync out signal
    // 11,Disable sync out signal
    ECap2Regs.ECEINT.all = 0; // enable Compare Equal Int
    // bit15-8      0's:    reserved
    // bit7         0:      CTR=CMP, 0 = compare interrupt disabled
    // bit6         0:      CTR=PRD, 0 = period interrupt disabled
    // bit5         0:      CTROVF, 0 = overflow interrupt disabled
    // bit4         0:      CEVT4, 1 = event 4 interrupt enabled
    // bit3         0:      CEVT3, 0 = event 3 interrupt disabled
    // bit2         0:      CEVT2, 0 = event 2 interrupt disabled
    // bit1         0:      CEVT1, 0 = event 1 interrupt disabled
    // bit0         0:      reserved

    ECap2Regs.CAP1 = APWM_PERIOD-1;         // Set Period value, 3750 = 150MHz/40kHz,
    ECap2Regs.CAP2 = 0;//500;           // Set Compare value
    ECap2Regs.CTRPHS = 0;//3875;//5625;//7020;//4680;


    // Setup APWM mode on CAP3, set period and compare registers
    ECap3Regs.ECCTL2.bit.CAP_APWM = 1;  // Active low
    // 0 ECAP module operates in capture mode. This mode forces the following configuration:
        //· Inhibits TSCTR resets via CTR = PRD event
        //· Inhibits shadow loads on CAP1 and 2 registers
        //· Permits user to enable CAP1-4 register load
        //· CAPx/APWMx pin operates as a capture input
    // 1 ECAP module operates in APWM mode. This mode forces the following configuration:
        //· Resets TSCTR on CTR = PRD event (period boundary
        //· Permits shadow loading on CAP1 and 2 registers
        //· Disables loading of time-stamps into CAP1-4 registers
        //· CAPx/APWMx pin operates as a APWM output

    ECap3Regs.ECCTL2.bit.APWMPOL = 0;
    // 0, Output is active high;
    // 1, Output is active low;
    ECap3Regs.ECCTL2.bit.SYNCI_EN = 1;  // Enable sync mode
    // 0, Disable sync-in option
    // 1, Enable counter(TSCTR) to be loaded from CTRPHS register upon either a SYNCI signal or a S/W force event
    ECap3Regs.ECCTL2.bit.SYNCO_SEL = 0;
    // 00,select sync-in event to be the sync-out signal (pass through)
    // 01,select CTR = PRD event to be the sync-out signal
    // 10,Disable sync out signal
    // 11,Disable sync out signal
    ECap3Regs.ECEINT.all = 0; // enable Compare Equal Int
    // bit15-8      0's:    reserved
    // bit7         0:      CTR=CMP, 0 = compare interrupt disabled
    // bit6         0:      CTR=PRD, 0 = period interrupt disabled
    // bit5         0:      CTROVF, 0 = overflow interrupt disabled
    // bit4         0:      CEVT4, 1 = event 4 interrupt enabled
    // bit3         0:      CEVT3, 0 = event 3 interrupt disabled
    // bit2         0:      CEVT2, 0 = event 2 interrupt disabled
    // bit1         0:      CEVT1, 0 = event 1 interrupt disabled
    // bit0         0:      reserved

    ECap3Regs.CAP1 = APWM_PERIOD-1;         // Set Period value, 3750 = 150MHz/40kHz
    ECap3Regs.CAP2 = 0;//500;           // Set Compare value
    ECap3Regs.CTRPHS = 4680;        // Set Compare value

    // Setup APWM mode on CAP3, set period and compare registers
    ECap4Regs.ECCTL2.bit.CAP_APWM = 1;  // Active low
    // 0 ECAP module operates in capture mode. This mode forces the following configuration:
        //· Inhibits TSCTR resets via CTR = PRD event
        //· Inhibits shadow loads on CAP1 and 2 registers
        //· Permits user to enable CAP1-4 register load
        //· CAPx/APWMx pin operates as a capture input
    // 1 ECAP module operates in APWM mode. This mode forces the following configuration:
        //· Resets TSCTR on CTR = PRD event (period boundary
        //· Permits shadow loading on CAP1 and 2 registers
        //· Disables loading of time-stamps into CAP1-4 registers
        //· CAPx/APWMx pin operates as a APWM output

    ECap4Regs.ECCTL2.bit.APWMPOL = 0;
    // 0, Output is active high;
    // 1, Output is active low;
    ECap4Regs.ECCTL2.bit.SYNCI_EN = 1;  // Enable sync mode
    // 0, Disable sync-in option
    // 1, Enable counter(TSCTR) to be loaded from CTRPHS register upon either a SYNCI signal or a S/W force event
    ECap4Regs.ECCTL2.bit.SYNCO_SEL = 0;
    // 00,select sync-in event to be the sync-out signal (pass through)
    // 01,select CTR = PRD event to be the sync-out signal
    // 10,Disable sync out signal
    // 11,Disable sync out signal
    ECap4Regs.ECEINT.all = 0; // enable Compare Equal Int
    // bit15-8      0's:    reserved
    // bit7         0:      CTR=CMP, 0 = compare interrupt disabled
    // bit6         0:      CTR=PRD, 0 = period interrupt disabled
    // bit5         0:      CTROVF, 0 = overflow interrupt disabled
    // bit4         0:      CEVT4, 1 = event 4 interrupt enabled
    // bit3         0:      CEVT3, 0 = event 3 interrupt disabled
    // bit2         0:      CEVT2, 0 = event 2 interrupt disabled
    // bit1         0:      CEVT1, 0 = event 1 interrupt disabled
    // bit0         0:      reserved

    ECap4Regs.CAP1 = APWM_PERIOD-1;         // Set Period value, 3750 = 150MHz/40kHz
    ECap4Regs.CAP2 = 0;//500;           // Set Compare value
    ECap4Regs.CTRPHS = 1875;        //
    // Start counters//使能计数器开始计数
    ECap1Regs.ECCTL2.bit.TSCTRSTOP = 1;
    ECap2Regs.ECCTL2.bit.TSCTRSTOP = 1;
    ECap3Regs.ECCTL2.bit.TSCTRSTOP = 1;
    ECap4Regs.ECCTL2.bit.TSCTRSTOP = 1;

//  Configure eCAP5 unit to test frequency
//GpioCtrlRegs.GPBPUD.bit.GPIO48 = 0;    // Enable pull-up on GPIO48 (CAP5)
//GpioCtrlRegs.GPBQSEL2.bit.GPIO48 = 0; // Synch to SYSCLKOUT GPIO48 (CAP5)

    ECap5Regs.ECEINT.all = 0x0000;          // Disable all eCAP interrupts
    ECap5Regs.ECCLR.all = 0xFFFF;       // Clear all CAP interrupt flags
    ECap5Regs.ECCTL1.bit.CAPLDEN = 0;   // Disabled loading of capture results
    ECap5Regs.ECCTL2.bit.TSCTRSTOP = 0; // Stop the counter

    ECap5Regs.ECCTL2.bit.CONT_ONESHT = 0;           // 0-continuous mode
    ECap5Regs.ECCTL2.bit.STOP_WRAP   = 0;           // Wrap after Capture Event 1 in continuous mode
    ECap5Regs.ECCTL1.bit.CAP1POL     = 1;           // 0-Capture Event 1 triggered on a rising  edge (RE)
                                                    // 1-Capture Event 1 triggered on a falling edge (FE)
    ECap5Regs.ECCTL1.bit.CAP2POL     = 0;
    ECap5Regs.ECCTL1.bit.CAP3POL     = 0;
    ECap5Regs.ECCTL1.bit.CAP4POL     = 0;
    ECap5Regs.ECCTL1.bit.CTRRST1     = 1;           // 1-Reset counter after Event 1 time-stamp has been captured (used in difference mode operation)
    ECap5Regs.ECCTL1.bit.CTRRST2     = 1;
    ECap5Regs.ECCTL1.bit.CTRRST3     = 1;
    ECap5Regs.ECCTL1.bit.CTRRST4     = 1;
    ECap5Regs.ECCTL2.bit.SYNCI_EN    = 0;           // 0-Disable sync-in option
    ECap5Regs.ECCTL2.bit.SYNCO_SEL   = 2;           // 2-Disable sync out signal
    ECap5Regs.ECCTL1.bit.PRESCALE    = 0;           // Event Filter prescale : 0-Divide by 1
    ECap5Regs.ECCTL2.bit.TSCTRSTOP   = 1;           // Time Stamp (TSCTR) Counter Stop (freeze) Control : 1-free-running
    ECap5Regs.ECCTL2.bit.REARM       = 1;           // Arms the one-shot sequence as follows:
                                                    // 1) Resets the Mod4 counter to zero
                                                    // 2) Unfreezes the Mod4 counter
                                                    // 3) Enables capture register loads
    ECap5Regs.ECCTL1.bit.CAPLDEN     = 1;           // Enable CAP1-4 register loads at capture event time.
    ECap5Regs.ECEINT.bit.CEVT1       = 1;           // Enable Capture Event 1 as an interrupt source

//  Configure eCAP6 unit to test S-phase grid's frequency
    ECap6Regs.ECEINT.all             = 0x0000;      // Disable all eCAP interrupts
    ECap6Regs.ECCLR.all              = 0xFFFF;      // Clear all CAP interrupt flags
    ECap6Regs.ECCTL1.bit.CAPLDEN     = 0;           // Disabled loading of capture results
    ECap6Regs.ECCTL2.bit.TSCTRSTOP   = 0;           // Stop the counter

    ECap6Regs.ECCTL2.bit.CONT_ONESHT = 0;
    ECap6Regs.ECCTL2.bit.STOP_WRAP   = 0;
    ECap6Regs.ECCTL1.bit.CAP1POL     = 0;
    ECap6Regs.ECCTL1.bit.CAP2POL     = 0;
    ECap6Regs.ECCTL1.bit.CAP3POL     = 0;
    ECap6Regs.ECCTL1.bit.CAP4POL     = 0;
    ECap6Regs.ECCTL1.bit.CTRRST1     = 1;
    ECap6Regs.ECCTL1.bit.CTRRST2     = 1;
    ECap6Regs.ECCTL1.bit.CTRRST3     = 1;
    ECap6Regs.ECCTL1.bit.CTRRST4     = 1;
    ECap6Regs.ECCTL2.bit.SYNCI_EN    = 0;
    ECap6Regs.ECCTL2.bit.SYNCO_SEL   = 2;
    ECap6Regs.ECCTL1.bit.PRESCALE    = 0;
    ECap6Regs.ECCTL2.bit.TSCTRSTOP   = 1;
    ECap6Regs.ECCTL2.bit.REARM       = 1;
    ECap6Regs.ECCTL1.bit.CAPLDEN     = 1;
    ECap6Regs.ECEINT.bit.CEVT1       = 1;

    PieCtrlRegs.PIEIER4.bit.INTx5    = 1;           // Enable ECAP5_INT in PIE group 4
    PieCtrlRegs.PIEIER4.bit.INTx6    = 1;           // Enable ECAP6_INT in PIE group 4
    IER |= M_INT4;                                  // Enable INT4 in IER to enable PIE group 4
}
//
// InitECapGpio - This function initializes GPIO pins to function as ECAP pins
//
// Each GPIO pin can be configured as a GPIO pin or up to 3 different
// peripheral functional pins. By default all pins come up as GPIO
// inputs after reset.
//
// Caution:
// For each eCAP peripheral
// Only one GPIO pin should be enabled for ECAP operation.
// Comment out other unwanted lines.
//
void 
InitECapGpio()
{
    InitECap1Gpio();
#if (DSP28_ECAP2)
    InitECap2Gpio();
#endif // endif DSP28_ECAP2
#if (DSP28_ECAP3)
    InitECap3Gpio();
#endif // endif DSP28_ECAP3
#if (DSP28_ECAP4)
    InitECap4Gpio();
#endif // endif DSP28_ECAP4
#if (DSP28_ECAP5)
    InitECap5Gpio();
#endif // endif DSP28_ECAP5
#if (DSP28_ECAP6)
    InitECap6Gpio();
#endif // endif DSP28_ECAP6
}

//
// InitECap1Gpio - 
//
void 
InitECap1Gpio(void)
{
    EALLOW;
    
    //
    // Enable internal pull-up for the selected pins
    // Pull-ups can be enabled or disabled by the user.
    // This will enable the pullups for the specified pins.
    // Comment out other unwanted lines.
    //
    //GpioCtrlRegs.GPAPUD.bit.GPIO5 = 0;    // Enable pull-up on GPIO5 (CAP1)
    GpioCtrlRegs.GPAPUD.bit.GPIO24 = 0;     // Enable pull-up on GPIO24 (CAP1)
    //GpioCtrlRegs.GPBPUD.bit.GPIO34 = 0;   // Enable pull-up on GPIO34 (CAP1)

    //
    // Inputs are synchronized to SYSCLKOUT by default.
    // Comment out other unwanted lines.
    //
    //GpioCtrlRegs.GPAQSEL1.bit.GPIO5 = 0;   //Synch to SYSCLKOUT GPIO5 (CAP1)
    GpioCtrlRegs.GPAQSEL2.bit.GPIO24 = 0;   //Synch to SYSCLKOUT GPIO24 (CAP1)
    //GpioCtrlRegs.GPBQSEL1.bit.GPIO34 = 0;  //Synch to SYSCLKOUT GPIO34 (CAP1)

    //
    // Configure eCAP-1 pins using GPIO regs
    // This specifies which of the possible GPIO pins will be eCAP1 functional pins.
    // Comment out other unwanted lines.
    //
    //GpioCtrlRegs.GPAMUX1.bit.GPIO5 = 3;    // Configure GPIO5 as CAP1
    GpioCtrlRegs.GPAMUX2.bit.GPIO24 = 1;    // Configure GPIO24 as CAP1
    //GpioCtrlRegs.GPBMUX1.bit.GPIO34 = 1;   // Configure GPIO24 as CAP1

    EDIS;
}

#if DSP28_ECAP2
//
// InitECap2Gpio -
//
void 
InitECap2Gpio(void)
{
    EALLOW;
    
    //
    // Enable internal pull-up for the selected pins
    // Pull-ups can be enabled or disabled by the user.
    // This will enable the pullups for the specified pins.
    // Comment out other unwanted lines.
    //
    GpioCtrlRegs.GPAPUD.bit.GPIO7 = 0;     // Enable pull-up on GPIO7 (CAP2)
    //GpioCtrlRegs.GPAPUD.bit.GPIO25 = 0;    // Enable pull-up on GPIO25 (CAP2)
    //GpioCtrlRegs.GPBPUD.bit.GPIO37 = 0;    // Enable pull-up on GPIO37 (CAP2)

    //
    // Inputs are synchronized to SYSCLKOUT by default.
    // Comment out other unwanted lines.
    //
    GpioCtrlRegs.GPAQSEL1.bit.GPIO7 = 0;    //Synch to SYSCLKOUT GPIO7 (CAP2)
    //GpioCtrlRegs.GPAQSEL2.bit.GPIO25 = 0;  //Synch to SYSCLKOUT GPIO25 (CAP2)
    //GpioCtrlRegs.GPBQSEL1.bit.GPIO37 = 0;  //Synch to SYSCLKOUT GPIO37 (CAP2)

    //
    // Configure eCAP-2 pins using GPIO regs
    // This specifies which of the possible GPIO pins will be eCAP2 functional 
    // pins. Comment out other unwanted lines.
    //
    GpioCtrlRegs.GPAMUX1.bit.GPIO7 = 3;    // Configure GPIO7 as CAP2
    //GpioCtrlRegs.GPAMUX2.bit.GPIO25 = 1;   // Configure GPIO25 as CAP2
    //GpioCtrlRegs.GPBMUX1.bit.GPIO37 = 3;   // Configure GPIO37 as CAP2

    EDIS;
}
#endif // endif DSP28_ECAP2

#if DSP28_ECAP3
//
// InitECap3Gpio - 
//
void 
InitECap3Gpio(void)
{
   EALLOW;

/* Enable internal pull-up for the selected pins */
// Pull-ups can be enabled or disabled by the user.
// This will enable the pullups for the specified pins.
// Comment out other unwanted lines.

   GpioCtrlRegs.GPAPUD.bit.GPIO9 = 0;      // Enable pull-up on GPIO9 (CAP3)
// GpioCtrlRegs.GPAPUD.bit.GPIO26 = 0;     // Enable pull-up on GPIO26 (CAP3)

// Inputs are synchronized to SYSCLKOUT by default.
// Comment out other unwanted lines.

   GpioCtrlRegs.GPAQSEL1.bit.GPIO9 = 0;    // Synch to SYSCLKOUT GPIO9 (CAP3)
// GpioCtrlRegs.GPAQSEL2.bit.GPIO26 = 0;   // Synch to SYSCLKOUT GPIO26 (CAP3)

/* Configure eCAP-3 pins using GPIO regs*/
// This specifies which of the possible GPIO pins will be eCAP3 functional pins.
// Comment out other unwanted lines.

   GpioCtrlRegs.GPAMUX1.bit.GPIO9 = 3;     // Configure GPIO9 as CAP3
// GpioCtrlRegs.GPAMUX2.bit.GPIO26 = 1;    // Configure GPIO26 as CAP3

    EDIS;
}
#endif // endif DSP28_ECAP3


#if DSP28_ECAP4
void InitECap4Gpio(void)
{
   EALLOW;

/* Enable internal pull-up for the selected pins */
// Pull-ups can be enabled or disabled by the user.
// This will enable the pullups for the specified pins.
// Comment out other unwanted lines.

   GpioCtrlRegs.GPAPUD.bit.GPIO11 = 0;   // Enable pull-up on GPIO11 (CAP4)
// GpioCtrlRegs.GPAPUD.bit.GPIO27 = 0;   // Enable pull-up on GPIO27 (CAP4)

// Inputs are synchronized to SYSCLKOUT by default.
// Comment out other unwanted lines.

   GpioCtrlRegs.GPAQSEL1.bit.GPIO11 = 0; // Synch to SYSCLKOUT GPIO11 (CAP4)
// GpioCtrlRegs.GPAQSEL2.bit.GPIO27 = 0; // Synch to SYSCLKOUT GPIO27 (CAP4)

/* Configure eCAP-4 pins using GPIO regs*/
// This specifies which of the possible GPIO pins will be eCAP4 functional pins.
// Comment out other unwanted lines.

   GpioCtrlRegs.GPAMUX1.bit.GPIO11 = 3;  // Configure GPIO11 as CAP4
// GpioCtrlRegs.GPAMUX2.bit.GPIO27 = 1;  // Configure GPIO27 as CAP4

    EDIS;
}
#endif // endif DSP28_ECAP4


#if DSP28_ECAP5
void InitECap5Gpio(void)
{
   EALLOW;

/* Enable internal pull-up for the selected pins */
// Pull-ups can be enabled or disabled by the user.
// This will enable the pullups for the specified pins.
// Comment out other unwanted lines.

   GpioCtrlRegs.GPAPUD.bit.GPIO3 = 0;     // Enable pull-up on GPIO3 (CAP5)
// GpioCtrlRegs.GPBPUD.bit.GPIO48 = 0;    // Enable pull-up on GPIO48 (CAP5)

// Inputs are synchronized to SYSCLKOUT by default.
// Comment out other unwanted lines.

   GpioCtrlRegs.GPAQSEL1.bit.GPIO3 = 0;  // Synch to SYSCLKOUT GPIO3 (CAP5)
// GpioCtrlRegs.GPBQSEL2.bit.GPIO48 = 0; // Synch to SYSCLKOUT GPIO48 (CAP5)

/* Configure eCAP-5 pins using GPIO regs*/
// This specifies which of the possible GPIO pins will be eCAP5 functional pins.
// Comment out other unwanted lines.

   GpioCtrlRegs.GPAMUX1.bit.GPIO3 = 2;   // Configure GPIO3 as CAP5
// GpioCtrlRegs.GPBMUX2.bit.GPIO48 = 1;  // Configure GPIO48 as CAP5

    EDIS;
}
#endif // endif DSP28_ECAP5


#if DSP28_ECAP6
void InitECap6Gpio(void)
{
   EALLOW;

/* Enable internal pull-up for the selected pins */
// Pull-ups can be enabled or disabled by the user.
// This will enable the pullups for the specified pins.
// Comment out other unwanted lines.

   GpioCtrlRegs.GPAPUD.bit.GPIO1 = 0;     // Enable pull-up on GPIO1 (CAP6)
// GpioCtrlRegs.GPBPUD.bit.GPIO49 = 0;    // Enable pull-up on GPIO49 (CAP6)

// Inputs are synchronized to SYSCLKOUT by default.
// Comment out other unwanted lines.

   GpioCtrlRegs.GPAQSEL1.bit.GPIO1 = 0;  // Synch to SYSCLKOUT GPIO1 (CAP6)
// GpioCtrlRegs.GPBQSEL2.bit.GPIO49 = 0; // Synch to SYSCLKOUT GPIO49 (CAP6)

/* Configure eCAP-5 pins using GPIO regs*/
// This specifies which of the possible GPIO pins will be eCAP6 functional pins.
// Comment out other unwanted lines.

   GpioCtrlRegs.GPAMUX1.bit.GPIO1 = 2;   // Configure GPIO1 as CAP6
// GpioCtrlRegs.GPBMUX2.bit.GPIO49 = 1;  // Configure GPIO49 as CAP6

    EDIS;
}
#endif // endif DSP28_ECAP6



//===========================================================================
// End of file.
//===========================================================================
